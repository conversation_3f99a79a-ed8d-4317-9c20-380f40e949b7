import { create } from "zustand";
import { persist } from "zustand/middleware";
import { supabase } from "@/lib/supabase";
import type { Tables } from "@dnd-cms/supabase/database.types";
import { useCampaignStore } from "./useCampaignStore";
import { getUser } from "@/utils/auth/auth";
import z from "zod";

export enum BLOCK_TYPE {
  TEXT = "TEXT",
  STATBLOCK = "STATBLOCK",
}

export type BlockType = Tables<"t_block_type">;

export type Block = Tables<"t_block"> & {
  block_type: BlockType;
  name: BLOCK_TYPE;
};

export type Note = Tables<"t_note"> & {
  blocks: Block[];
};

export type Folder = Tables<"t_folder"> & {
  notes: Note[];
};

export const upsertBlockSchema = z.object({
  id: z.uuid().optional(),
  title: z.string(),
  content: z.string().optional(),
  blockType: z.enum([BLOCK_TYPE.TEXT, BLOCK_TYPE.STATBLOCK]),
  noteId: z.string(),
});

export type UpsertBlockFormData = z.infer<typeof upsertBlockSchema>;

interface FolderState {
  folders: Folder[];
  fetchFolders: ({ includeDeleted }: { includeDeleted?: boolean }) => Promise<Folder[] | []>;
  createFolder: (name: string) => Promise<Folder | null>;
  deleteFolder: (id: string) => Promise<void>;
  createNote: ({
    title,
    content,
    folderId,
  }: {
    title: string;
    content?: string;
    folderId: string;
  }) => Promise<Note | undefined>;
  updateNote: (noteId: string, data: Partial<Note>) => Promise<Note | undefined>;
  recentNotes: Note[];
  openNote: (noteId: string) => void;
  closeNote: (noteId: string) => void;
  activeNote: Note | undefined;
  setActiveNote: (noteId: string) => void;
  upsertBlock: (payload: UpsertBlockFormData) => Promise<Note | undefined>;
}

export const useFolderStore = create<FolderState>()(
  persist(
    (set, get) => ({
      folders: [],
      recentNotes: [],
      activeNote: undefined,
      fetchFolders: async ({ includeDeleted = false }) => {
        try {
          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase.rpc("sp_fetch_folders", {
            _campaign_id: campaignstore.selectedCampaign.id,
            _include_deleted: includeDeleted,
          });

          if (error) {
            throw error;
          }

          const folders = data.map((row: Folder) => row).filter(Boolean) as Folder[];
          set({ folders });
          return folders;
        } catch (error) {
          console.error("Error fetching folders:", error);
          return [];
        }
      },
      createFolder: async (name: string) => {
        try {
          const user = await getUser();

          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase
            .from("t_folder")
            .insert([{ name, created_by: user.id, campaign_id: campaignstore.selectedCampaign.id }])
            .select()
            .single();

          if (error) {
            throw error;
          }

          await get().fetchFolders({});

          return data;
        } catch (error) {
          console.error("Error creating folder:", error);
          return null;
        }
      },
      deleteFolder: async (id: string) => {
        try {
          const { error } = await supabase
            .from("t_folder")
            .update({ is_deleted: true })
            .eq("id", id);

          if (error) {
            throw error;
          }

          await get().fetchFolders({});
        } catch (error) {
          console.error("Error deleting folder:", error);
        }
      },
      createNote: async ({ title, content, folderId }) => {
        try {
          const user = await getUser();

          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase
            .from("t_note")
            .insert([
              {
                title,
                content,
                folder_id: folderId,
                created_by: user.id,
                campaign_id: campaignstore.selectedCampaign.id,
              },
            ])
            .select()
            .single();

          if (error) {
            throw error;
          }

          if (!data) {
            return undefined;
          }

          await get().fetchFolders({});

          return data as Note;
        } catch (error) {
          console.error("Error creating note:", error);
          return undefined;
        }
      },
      updateNote: async (noteId, data) => {
        try {
          const user = await getUser();

          const { data: updatedNote, error } = await supabase
            .from("t_note")
            .update({
              title: data.title,
              content: data.content,
              folderId: data.folder_id,
              updatedBy: user.id,
            })
            .eq("id", noteId)
            .select()
            .single();

          if (error) {
            throw error;
          }

          if (!updatedNote) {
            return undefined;
          }

          await get().fetchFolders({});

          set({
            activeNote: updatedNote,
          });

          return updatedNote as Note;
        } catch (error) {
          console.error("Error updating note:", error);
          return undefined;
        }
      },
      openNote: (noteId: string) => {
        const folder = get().folders.find((folder) =>
          folder.notes.some((note) => note.id === noteId),
        );
        if (!folder) return;

        const note = folder.notes.find((note) => note.id === noteId);
        if (!note) return;

        set((state) => {
          const updatedRecentNotes = [note!, ...state.recentNotes.filter((n) => n.id !== noteId)];

          return { recentNotes: updatedRecentNotes, activeNote: note };
        });
      },
      closeNote: (noteId: string) => {
        set((state) => {
          const updatedRecentNotes = state.recentNotes.filter((note) => note.id !== noteId);

          let newActiveNote = state.activeNote;
          if (state.activeNote?.id === noteId) {
            const currentIndex = state.recentNotes.findIndex((note) => note.id === noteId);
            if (updatedRecentNotes.length > 0) {
              const nextIndex = currentIndex < updatedRecentNotes.length ? currentIndex : 0;
              newActiveNote = updatedRecentNotes[nextIndex];
            } else {
              newActiveNote = undefined;
            }
          }

          return {
            recentNotes: updatedRecentNotes,
            activeNote: newActiveNote,
          };
        });
      },
      setActiveNote: (noteId: string) => {
        const note = get().recentNotes.find((note) => note.id === noteId);
        if (note) {
          set({ activeNote: note });
        }
      },
      upsertBlock: async (payload) => {
        try {
          const user = await getUser();

          const { data: blockTypeData, error: blockTypeError } = await supabase
            .from("t_block_type")
            .select()
            .eq("name", payload.blockType)
            .single();

          if (blockTypeError) {
            throw blockTypeError;
          }

          const { data, error } = await supabase.rpc("sp_upsert_block", {
            _id: payload.id ?? null,
            _title: payload.title,
            _content: payload.content,
            _block_type: blockTypeData.id,
            _note_id: payload.noteId,
            _created_by: user.id,
          });

          if (error) {
            throw error;
          }

          const folders = await get().fetchFolders({});

          const relaventNote = folders
            .flatMap((folder) => folder.notes)
            .find((note) => note.id === payload.noteId);

          if (relaventNote) {
            set({
              activeNote: relaventNote,
            });
          }

          return data[0];
        } catch (error) {
          console.error(error);
          return undefined;
        }
      },
    }),
    {
      name: "folder-store",
    },
  ),
);
