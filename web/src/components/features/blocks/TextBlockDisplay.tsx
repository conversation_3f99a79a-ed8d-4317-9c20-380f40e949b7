import { Card } from "@/components/ui/Card";
import type { Block } from "@/stores/useFolderStore";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { EditorTheme } from "@/components/features/editor/EditorTheme";
import { useEffect } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";

const ReadOnlyPlugin = ({ content }: { content: string | null }) => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (content) {
      try {
        const editorState = editor.parseEditorState(content);
        editor.setEditorState(editorState);
      } catch (error) {
        console.error("Error parsing editor state:", error);
      }
    }
  }, [editor, content]);

  return null;
};

const initialConfig = {
  namespace: "ReadOnlyEditor",
  theme: EditorTheme,
  nodes: [
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    CodeNode,
    CodeHighlightNode,
    TableNode,
    TableCellNode,
    TableRowNode,
    AutoLinkNode,
    LinkNode,
  ],
  editable: false,
  onError: (error: Error) => {
    console.error(error);
  },
};

export const TextBlockDisplay = ({ block }: { block: Block }) => {
  return (
    <Card>
      <Card.Header>
        <Card.Title>{block.title}</Card.Title>
      </Card.Header>
      <Card.Content>
        <LexicalComposer initialConfig={initialConfig}>
          <RichTextPlugin
            contentEditable={<ContentEditable className="editor-content-editable outline-none" />}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <ReadOnlyPlugin content={block.content} />
        </LexicalComposer>
      </Card.Content>
    </Card>
  );
};
