import { Card } from "@/components/ui/Card";
import { Separator } from "@/components/ui/Separator";

enum AttributeShortcode {
  strength = "STR",
  dexterity = "DEX",
  constitution = "CON",
  intelligence = "INT",
  wisdom = "WIS",
  charisma = "CHA",
}

export type SkillKey =
  | "acrobatics"
  | "animalHandling"
  | "arcana"
  | "athletics"
  | "deception"
  | "history"
  | "insight"
  | "intimidation"
  | "investigation"
  | "medicine"
  | "nature"
  | "perception"
  | "performance"
  | "persuasion"
  | "religion"
  | "sleightOfHand"
  | "stealth"
  | "survival";

export const strengthSkills: Skill<PERSON>ey[] = ["athletics"];
export const dexteritySkills: Skill<PERSON><PERSON>[] = ["acrobatics", "sleightOfHand", "stealth"];
export const constitutionSkills: Skill<PERSON>ey[] = [];
export const intelligenceSkills: Skill<PERSON>ey[] = [
  "arcana",
  "history",
  "investigation",
  "nature",
  "religion",
];
export const wisdomSkills: Ski<PERSON><PERSON><PERSON>[] = [
  "animalHandling",
  "insight",
  "medicine",
  "perception",
  "survival",
];
export const charismaSkills: Ski<PERSON><PERSON><PERSON>[] = [
  "deception",
  "intimidation",
  "performance",
  "persuasion",
];

export type AttributeKey = keyof typeof AttributeShortcode;

export type Attribute = {
  baseValue: number;
};

export type Attributes = {
  [key in AttributeKey]: Attribute;
};

export type Skill = {
  override?: number;
  value: number;
  modifier: number;
};

export type Skills = {
  [key in SkillKey]?: Skill;
};

const statblock = {
  name: "Acolyte",
  description:
    "Acolytes are junior members of a clergy, usually answerable to a priest. They perform a variety of functions in a temple and are granted minor spellcasting power by their deities.",
  attributes: {
    strength: {
      baseValue: 8,
    },
    dexterity: {
      baseValue: 10,
    },
    constitution: {
      baseValue: 12,
    },
    intelligence: {
      baseValue: 13,
    },
    wisdom: {
      baseValue: 14,
    },
    charisma: {
      baseValue: 15,
    },
  },
  skills: {},
};

const calculateSkillValue = ({ attribute, skill }: { attribute: Attribute; skill: Skill; }) => {
  const value = attribute.baseValue + (skill?.override ?? 0);
  const modifier = Math.floor(value / 2) - 5;

  return {
    override: skill?.override,
    value,
    modifier,
  }
};

const calculateSkills = ({ attributes, skills }: { attributes: Attributes; skills: Skills }) => {
  const calculatedSkills: Skills = {};
  for (const [attributeKey, attribute] of Object.entries(attributes)) {
    if (attributeKey === "strength") {
      strengthSkills.forEach((skillKey) => {
        const { override, value, modifier } = calculateSkillValue({ attribute, skill: skills[skillKey] || {} });
        calculatedSkills[skillKey] = {
          override,
          value,
          modifier,
        };
      });
    }

    if (attributeKey === "dexterity") {
      dexteritySkills.forEach((skillKey) => {
        const { override, value, modifier } = calculateSkillValue({ attribute, skill: skills[skillKey] });
        calculatedSkills[skillKey] = {
          override,
          value,
          modifier,
        };
      });
    }

    if (attributeKey === "constitution") {
      constitutionSkills.forEach((skillKey) => {
        const value = attribute.baseValue + (skills[skillKey]?.override ?? 0);
        const modifier = Math.floor(value / 2) - 5;
        calculatedSkills[skillKey] = {
          override: skills[skillKey]?.override,
          value,
          modifier,
        };
      });
    }
  }

  return calculatedSkills;
};

export const AttributeDisplay = ({
  attribute,
  value,
}: {
  attribute: AttributeShortcode;
  value: number;
}) => {
  return (
    <div className="border first:rounded-l-md last:rounded-r-md ring-1 ring-ring">
      <h3 className="scroll-m-20 text-xs font-semibold tracking-tight text-muted-foreground p-1 sm:p-2">
        {attribute}
      </h3>
      <Separator />
      <p className="text-lg p-1 sm:p-2">{value}</p>
      <Separator />
      <p className="text-sm p-1 sm:p-2">(+2)</p>
    </div>
  );
};

export const SkillsDisplay = ({
  attributes,
  skills,
}: {
  attributes: Attributes;
  skills: Skills;
}) => {
  const calculatedSkills = calculateSkills({ attributes, skills });
  return <div>{JSON.stringify(calculatedSkills)}</div>;
};

export const StatblockDisplay = () => {
  return (
    <Card>
      <Card.Header>
        <Card.Title>
          <h3 className="scroll-m-20 text-2xl font-semibold tracking-tight">{statblock.name}</h3>
          <p className="text-muted-foreground text-sm">{statblock.description}</p>
        </Card.Title>
      </Card.Header>
      <Separator />
      <Card.Content>
        <div className="flex flex-col gap-4 items-center sm:items-start">
          <div className="grid grid-cols-6 w-fit text-center rounded gap-0">
            {Object.entries(statblock.attributes).map(([key, value], index) => (
              <AttributeDisplay
                key={`${key}-${value}-${index}`}
                attribute={AttributeShortcode[key as keyof typeof AttributeShortcode]}
                value={value.baseValue}
              />
            ))}
          </div>
        </div>

        <SkillsDisplay attributes={statblock.attributes} skills={statblock.skills} />
      </Card.Content>
    </Card>
  );
};
